@extends('layouts.admin')

@section('title', 'แก้ไขบริการ - Admin Panel')

@php
use Illuminate\Support\Facades\Storage;
@endphp

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">
                        <i class="fas fa-edit me-2 text-primary"></i>แก้ไขบริการ
                    </h1>
                    <p class="text-muted">แก้ไขข้อมูลบริการ: {{ $service->title }}</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item">
                            <a href="{{ route('admin.dashboard') }}">
                                <i class="fas fa-home"></i> แดชบอร์ด
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{{ route('admin.services.index') }}">
                                <i class="fas fa-tools"></i> จัดการบริการ
                            </a>
                        </li>
                        <li class="breadcrumb-item active">
                            <i class="fas fa-edit"></i> แก้ไขบริการ
                        </li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row justify-content-center">
                <div class="col-lg-8 col-12">
                    <div class="card shadow-sm border-0">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-edit"></i> แก้ไขบริการ: {{ $service->title }}
                            </h5>
                        </div>
                        
                        <form action="{{ route('admin.services.update', $service) }}" method="POST" enctype="multipart/form-data" id="serviceForm">
                            @csrf
                            @method('PUT')
                            <input type="hidden" name="remove_image" id="removeImageFlag" value="0">
                            
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label for="title" class="form-label">ชื่อบริการ <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                                   id="title" name="title" value="{{ old('title', $service->title) }}" required>
                                            @error('title')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="description" class="form-label">รายละเอียดบริการ <span class="text-danger">*</span></label>
                                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                                      id="description" name="description" rows="5" required>{{ old('description', $service->description) }}</textarea>
                                            @error('description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="price" class="form-label">ราคา (บาท) <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <span class="input-group-text">฿</span>
                                                <input type="number" class="form-control @error('price') is-invalid @enderror" 
                                                       id="price" name="price" value="{{ old('price', $service->price) }}" 
                                                       min="0" step="0.01" required>
                                                @error('price')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="image" class="form-label">รูปภาพบริการ</label>
                                            <input type="file" class="form-control @error('image') is-invalid @enderror"
                                                   id="image" name="image" accept="image/*">
                                            <small class="form-text text-muted">รองรับไฟล์: JPG, PNG, GIF (ขนาดไม่เกิน 2MB)</small>
                                            @error('image')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        
                                        <div class="text-center">
                                            <label class="form-label">รูปภาพปัจจุบัน</label>

                                            <div id="imagePreview" @if($service->image) style="display: block;" @else style="display: none;" @endif>
                                                @if($service->image)
                                                    @php
                                                        // ตรวจสอบไฟล์ที่มีอยู่จริง
                                                        $imagePath = 'services/' . $service->image;
                                                        $imageExists = Storage::disk('public')->exists($imagePath);

                                                        // ถ้าไฟล์ไม่มี ลองหาไฟล์ที่มีชื่อคล้ายกัน
                                                        if (!$imageExists) {
                                                            $files = Storage::disk('public')->files('services');
                                                            $baseName = pathinfo($service->image, PATHINFO_FILENAME);
                                                            $extension = pathinfo($service->image, PATHINFO_EXTENSION);

                                                            foreach ($files as $file) {
                                                                $fileName = basename($file);
                                                                if (strpos($fileName, $baseName) !== false) {
                                                                    $imagePath = $file;
                                                                    $imageExists = true;
                                                                    break;
                                                                }
                                                            }
                                                        }

                                                        $imageUrl = $imageExists ? url('storage/' . $imagePath) : asset('images/no-image.svg');
                                                    @endphp
                                                    <img id="previewImg" src="{{ $imageUrl }}"
                                                         class="img-thumbnail" style="max-width: 100%; max-height: 200px;"
                                                         alt="รูปภาพบริการ"
                                                         onerror="console.log('Image error:', this.src); this.src='{{ asset('images/no-image.svg') }}'; this.onerror=null;">
                                                    @if(!$imageExists)
                                                        <div class="alert alert-warning mt-2">
                                                            <small><i class="fas fa-exclamation-triangle"></i> ไฟล์รูปภาพไม่พบ: {{ $service->image }}</small>
                                                        </div>
                                                    @endif
                                                @else
                                                    <img id="previewImg" src="{{ asset('images/no-image.svg') }}"
                                                         class="img-thumbnail" style="max-width: 100%; max-height: 200px;"
                                                         alt="ไม่มีรูปภาพ">
                                                @endif
                                                <div class="mt-2">
                                                    <button type="button" class="btn btn-sm btn-danger" id="removeImage">
                                                        <i class="fas fa-trash"></i> ลบรูปภาพ
                                                    </button>
                                                </div>
                                            </div>

                                            @if(!$service->image)
                                            <div id="noImageDisplay" class="border border-dashed rounded p-4 text-muted">
                                                <i class="fas fa-image fa-3x mb-2"></i>
                                                <p>ไม่มีรูปภาพ</p>
                                            </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card-footer bg-light">
                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('admin.services.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left"></i> กลับ
                                    </a>
                                    <div>
                                        <a href="{{ route('services.show', $service) }}" target="_blank" class="btn btn-info me-2">
                                            <i class="fas fa-eye"></i> ดูหน้าบ้าน
                                        </a>
                                        <button type="submit" class="btn btn-warning">
                                            <i class="fas fa-save"></i> อัปเดตบริการ
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    
                    <!-- Delete Section -->
                    <div class="card shadow-sm border-0 mt-3">
                        <div class="card-header bg-danger text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-trash"></i> ลบบริการ
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">การลบบริการนี้จะไม่สามารถกู้คืนได้ กรุณาตรวจสอบให้แน่ใจก่อนดำเนินการ</p>
                            <form action="{{ route('admin.services.destroy', $service) }}" method="POST" id="deleteForm">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger" onclick="return confirm('คุณแน่ใจหรือไม่ที่จะลบบริการนี้?')">
                                    <i class="fas fa-trash"></i> ลบบริการนี้
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

@push('styles')
<style>
.card {
    transition: transform 0.2s ease-in-out;
}
.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}
</style>
@endpush

@push('scripts')
<script>
$(document).ready(function() {
    // Store original image URL and filename
    const originalImageUrl = "{{ $service->image ? url('storage/services/' . $service->image) : asset('images/no-image.svg') }}";
    const originalImageName = "{{ $service->image }}";

    // Debug: แสดง URL ใน console
    console.log('Original Image URL:', originalImageUrl);
    console.log('Original Image Name:', originalImageName);

    // แสดงชื่อไฟล์ปัจจุบันในช่องเลือกไฟล์
    @if($service->image)
        // สร้าง label แสดงชื่อไฟล์ปัจจุบัน
        $('#image').after(`
            <div class="mt-1" id="currentFileName">
                <small class="text-primary">
                    <i class="fas fa-file-image"></i> ไฟล์ปัจจุบัน: <strong>${originalImageName}</strong>
                </small>
            </div>
        `);

        // ตรวจสอบว่ารูปโหลดได้หรือไม่
        $('#previewImg').on('load', function() {
            console.log('Image loaded successfully');
        }).on('error', function() {
            console.log('Image failed to load:', this.src);
            $(this).attr('src', '{{ asset("images/no-image.svg") }}');
        });
    @else
        // ถ้าไม่มีรูป ให้สร้าง div เปล่าไว้สำหรับแสดงข้อความ
        $('#image').after(`
            <div class="mt-1" id="currentFileName">
            </div>
        `);
    @endif

    // แสดงข้อความแจ้งเตือนเมื่ออัปเดทสำเร็จ
    @if(session('success'))
        Swal.fire({
            icon: 'success',
            title: 'สำเร็จ!',
            text: '{{ session('success') }}',
            timer: 3000,
            showConfirmButton: false
        });
    @endif

    // Image preview
    $('#image').on('change', function() {
        let file = this.files[0];
        if (file) {
            // ยกเลิกการลบรูปภาพ (ถ้ามี)
            $('#removeImageFlag').val('0');

            let reader = new FileReader();
            reader.onload = function(e) {
                $('#previewImg').attr('src', e.target.result);
                $('#imagePreview').show();
                $('#noImageDisplay').hide();

                // อัปเดทชื่อไฟล์ที่แสดง
                $('#currentFileName').html(`
                    <small class="text-success">
                        <i class="fas fa-file-image"></i> ไฟล์ใหม่: <strong>${file.name}</strong>
                    </small>
                `);

                // เปลี่ยนปุ่มกลับเป็นลบรูปภาพ
                $('#undoRemove, #removeImage').html('<i class="fas fa-trash"></i> ลบรูปภาพ')
                    .removeClass('btn-warning').addClass('btn-danger').attr('id', 'removeImage');
            };
            reader.readAsDataURL(file);
        }
    });

    // Remove image
    $('#removeImage').on('click', function() {
        if (confirm('คุณแน่ใจหรือไม่ที่จะลบรูปภาพนี้?')) {
            // ตั้งค่า flag ให้ลบรูปภาพ
            $('#removeImageFlag').val('1');
            $('#image').val('');

            // ซ่อนรูปภาพและแสดงข้อความไม่มีรูป
            $('#imagePreview').hide();
            $('#noImageDisplay').show();

            // ลบข้อความชื่อไฟล์ปัจจุบัน
            $('#currentFileName').html(`
                <small class="text-danger">
                    <i class="fas fa-trash"></i> รูปภาพจะถูกลบเมื่อกดบันทึก
                </small>
            `);

            // เปลี่ยนข้อความปุ่ม
            $(this).html('<i class="fas fa-undo"></i> ยกเลิกการลบ').removeClass('btn-danger').addClass('btn-warning');
            $(this).attr('id', 'undoRemove');
        }
    });

    // Undo remove image
    $(document).on('click', '#undoRemove', function() {
        // ยกเลิกการลบ
        $('#removeImageFlag').val('0');

        @if($service->image)
            $('#imagePreview').show();
            $('#noImageDisplay').hide();
            $('#previewImg').attr('src', originalImageUrl);

            // กลับไปแสดงชื่อไฟล์เดิม
            $('#currentFileName').html(`
                <small class="text-primary">
                    <i class="fas fa-file-image"></i> ไฟล์ปัจจุบัน: <strong>${originalImageName}</strong>
                </small>
            `);
        @endif

        // เปลี่ยนปุ่มกลับ
        $(this).html('<i class="fas fa-trash"></i> ลบรูปภาพ').removeClass('btn-warning').addClass('btn-danger');
        $(this).attr('id', 'removeImage');
    });

    // Form validation
    $('#serviceForm').on('submit', function(e) {
        let isValid = true;
        
        // Clear previous errors
        $('.form-control').removeClass('is-invalid');
        $('.invalid-feedback').remove();
        
        // Validate title
        if (!$('#title').val().trim()) {
            $('#title').addClass('is-invalid');
            $('#title').after('<div class="invalid-feedback">กรุณากรอกชื่อบริการ</div>');
            isValid = false;
        }
        
        // Validate description
        if (!$('#description').val().trim()) {
            $('#description').addClass('is-invalid');
            $('#description').after('<div class="invalid-feedback">กรุณากรอกรายละเอียดบริการ</div>');
            isValid = false;
        }
        
        // Validate price
        if (!$('#price').val() || $('#price').val() < 0) {
            $('#price').addClass('is-invalid');
            $('#price').parent().after('<div class="invalid-feedback">กรุณากรอกราคาที่ถูกต้อง</div>');
            isValid = false;
        }
        
        if (!isValid) {
            e.preventDefault();
        }
    });

    // Auto-resize textarea
    $('#description').on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
});
</script>
@endpush

@endsection
