@extends('layouts.admin')

@section('title', 'แก้ไขบริการ - Admin Panel')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">
                        <i class="fas fa-edit me-2 text-primary"></i>แก้ไขบริการ
                    </h1>
                    <p class="text-muted">แก้ไขข้อมูลบริการ: {{ $service->title }}</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item">
                            <a href="{{ route('admin.dashboard') }}">
                                <i class="fas fa-home"></i> แดชบอร์ด
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{{ route('admin.services.index') }}">
                                <i class="fas fa-tools"></i> จัดการบริการ
                            </a>
                        </li>
                        <li class="breadcrumb-item active">
                            <i class="fas fa-edit"></i> แก้ไขบริการ
                        </li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row justify-content-center">
                <div class="col-lg-8 col-12">
                    <div class="card shadow-sm border-0">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-edit"></i> แก้ไขบริการ: {{ $service->title }}
                            </h5>
                        </div>
                        
                        <form action="{{ route('admin.services.update', $service) }}" method="POST" enctype="multipart/form-data" id="serviceForm">
                            @csrf
                            @method('PUT')
                            
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label for="title" class="form-label">ชื่อบริการ <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                                   id="title" name="title" value="{{ old('title', $service->title) }}" required>
                                            @error('title')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="description" class="form-label">รายละเอียดบริการ <span class="text-danger">*</span></label>
                                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                                      id="description" name="description" rows="5" required>{{ old('description', $service->description) }}</textarea>
                                            @error('description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="price" class="form-label">ราคา (บาท) <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <span class="input-group-text">฿</span>
                                                <input type="number" class="form-control @error('price') is-invalid @enderror" 
                                                       id="price" name="price" value="{{ old('price', $service->price) }}" 
                                                       min="0" step="0.01" required>
                                                @error('price')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="image" class="form-label">รูปภาพบริการ</label>
                                            <input type="file" class="form-control @error('image') is-invalid @enderror" 
                                                   id="image" name="image" accept="image/*">
                                            <small class="form-text text-muted">รองรับไฟล์: JPG, PNG, GIF (ขนาดไม่เกิน 2MB)</small>
                                            @error('image')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        
                                        <div class="text-center">
                                            <label class="form-label">รูปภาพบริการ</label>

                                            @if($service->image)
                                                <div class="mb-3 p-3 bg-light rounded border">
                                                    <div class="d-flex align-items-center mb-2">
                                                        <i class="fas fa-info-circle text-info me-2"></i>
                                                        <strong class="text-dark">ข้อมูลรูปภาพปัจจุบัน</strong>
                                                    </div>

                                                    <div class="row g-2">
                                                        <div class="col-12">
                                                            <div class="d-flex align-items-center mb-1">
                                                                <i class="fas fa-file-image text-primary me-2" style="width: 16px;"></i>
                                                                <small class="text-muted me-2">ชื่อไฟล์:</small>
                                                                <span class="badge bg-primary">{{ $service->image }}</span>
                                                            </div>
                                                        </div>

                                                        <div class="col-12">
                                                            <div class="d-flex align-items-center mb-1">
                                                                <i class="fas fa-calendar-plus text-success me-2" style="width: 16px;"></i>
                                                                <small class="text-muted me-2">อัปโหลดเมื่อ:</small>
                                                                <small class="text-dark">{{ $service->created_at->format('d/m/Y H:i') }} น.</small>
                                                            </div>
                                                        </div>

                                                        @if($service->updated_at != $service->created_at)
                                                        <div class="col-12">
                                                            <div class="d-flex align-items-center mb-1">
                                                                <i class="fas fa-calendar-check text-warning me-2" style="width: 16px;"></i>
                                                                <small class="text-muted me-2">แก้ไขล่าสุด:</small>
                                                                <small class="text-dark">{{ $service->updated_at->format('d/m/Y H:i') }} น.</small>
                                                            </div>
                                                        </div>
                                                        @endif

                                                        <div class="col-12">
                                                            <div class="d-flex align-items-center">
                                                                <i class="fas fa-link text-secondary me-2" style="width: 16px;"></i>
                                                                <small class="text-muted me-2">URL:</small>
                                                                <small class="text-break">{{ $service->image_url }}</small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            @else
                                                <div class="mb-3 p-3 bg-light rounded border">
                                                    <div class="d-flex align-items-center mb-2">
                                                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                                        <small class="text-muted">ยังไม่มีรูปภาพสำหรับบริการนี้</small>
                                                    </div>
                                                </div>
                                            @endif

                                            <div id="imageDisplay">
                                                @if($service->image)
                                                    <img id="displayImg" src="{{ $service->image_url }}" class="img-thumbnail"
                                                         style="max-width: 100%; max-height: 200px;" alt="รูปภาพบริการ">
                                                @else
                                                    <div id="displayImg" class="border border-dashed rounded p-4 text-muted">
                                                        <i class="fas fa-image fa-3x mb-2"></i>
                                                        <p>ไม่มีรูปภาพ</p>
                                                    </div>
                                                @endif
                                            </div>

                                            <div class="mt-2" id="imageActions" style="display: none;">
                                                <button type="button" class="btn btn-sm btn-danger" id="removeImage">
                                                    <i class="fas fa-trash"></i> ยกเลิกรูปใหม่
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card-footer bg-light">
                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('admin.services.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left"></i> กลับ
                                    </a>
                                    <div>
                                        <a href="{{ route('services.show', $service) }}" target="_blank" class="btn btn-info me-2">
                                            <i class="fas fa-eye"></i> ดูหน้าบ้าน
                                        </a>
                                        <button type="submit" class="btn btn-warning">
                                            <i class="fas fa-save"></i> อัปเดตบริการ
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    
                    <!-- Delete Section -->
                    <div class="card shadow-sm border-0 mt-3">
                        <div class="card-header bg-danger text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-trash"></i> ลบบริการ
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">การลบบริการนี้จะไม่สามารถกู้คืนได้ กรุณาตรวจสอบให้แน่ใจก่อนดำเนินการ</p>
                            <form action="{{ route('admin.services.destroy', $service) }}" method="POST" id="deleteForm">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger" onclick="return confirm('คุณแน่ใจหรือไม่ที่จะลบบริการนี้?')">
                                    <i class="fas fa-trash"></i> ลบบริการนี้
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

@push('styles')
<style>
.card {
    transition: transform 0.2s ease-in-out;
}
.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}
</style>
@endpush

@push('scripts')
<script>
$(document).ready(function() {
    // Store original image URL
    const originalImageUrl = "{{ $service->image_url }}";

    // แสดงข้อความแจ้งเตือนเมื่ออัปเดทสำเร็จ
    @if(session('success'))
        Swal.fire({
            icon: 'success',
            title: 'สำเร็จ!',
            text: '{{ session('success') }}',
            timer: 3000,
            showConfirmButton: false
        });
    @endif

    // Image preview - แทนที่รูปปัจจุบัน
    $('#image').on('change', function() {
        let file = this.files[0];
        if (file) {
            let reader = new FileReader();
            reader.onload = function(e) {
                // แสดงรูปใหม่
                $('#displayImg').replaceWith(`
                    <img id="displayImg" src="${e.target.result}" class="img-thumbnail"
                         style="max-width: 100%; max-height: 200px;" alt="รูปภาพใหม่">
                `);

                // แสดงข้อมูลไฟล์ใหม่
                let fileInfo = `
                    <div class="alert alert-info mt-2" id="newFileInfo">
                        <i class="fas fa-upload"></i> <strong>รูปใหม่ที่เลือก:</strong><br>
                        <small>
                            <i class="fas fa-file"></i> ชื่อไฟล์: ${file.name}<br>
                            <i class="fas fa-weight"></i> ขนาด: ${(file.size / 1024 / 1024).toFixed(2)} MB<br>
                            <i class="fas fa-image"></i> ประเภท: ${file.type}
                        </small>
                    </div>
                `;

                // ลบข้อมูลไฟล์เก่าถ้ามี
                $('#newFileInfo').remove();
                $('#imageDisplay').after(fileInfo);

                $('#imageActions').show();
            };
            reader.readAsDataURL(file);
        }
    });

    // Remove image - กลับไปใช้รูปเดิม
    $('#removeImage').on('click', function() {
        $('#image').val('');

        // กลับไปแสดงรูปเดิม
        @if($service->image)
            $('#displayImg').replaceWith(`
                <img id="displayImg" src="${originalImageUrl}" class="img-thumbnail"
                     style="max-width: 100%; max-height: 200px;" alt="รูปภาพบริการ">
            `);
        @else
            $('#displayImg').replaceWith(`
                <div id="displayImg" class="border border-dashed rounded p-4 text-muted">
                    <i class="fas fa-image fa-3x mb-2"></i>
                    <p>ไม่มีรูปภาพ</p>
                </div>
            `);
        @endif

        // ลบข้อมูลไฟล์ใหม่
        $('#newFileInfo').remove();
        $('#imageActions').hide();
    });

    // Form validation
    $('#serviceForm').on('submit', function(e) {
        let isValid = true;
        
        // Clear previous errors
        $('.form-control').removeClass('is-invalid');
        $('.invalid-feedback').remove();
        
        // Validate title
        if (!$('#title').val().trim()) {
            $('#title').addClass('is-invalid');
            $('#title').after('<div class="invalid-feedback">กรุณากรอกชื่อบริการ</div>');
            isValid = false;
        }
        
        // Validate description
        if (!$('#description').val().trim()) {
            $('#description').addClass('is-invalid');
            $('#description').after('<div class="invalid-feedback">กรุณากรอกรายละเอียดบริการ</div>');
            isValid = false;
        }
        
        // Validate price
        if (!$('#price').val() || $('#price').val() < 0) {
            $('#price').addClass('is-invalid');
            $('#price').parent().after('<div class="invalid-feedback">กรุณากรอกราคาที่ถูกต้อง</div>');
            isValid = false;
        }
        
        if (!isValid) {
            e.preventDefault();
        }
    });

    // Auto-resize textarea
    $('#description').on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
});
</script>
@endpush

@endsection
